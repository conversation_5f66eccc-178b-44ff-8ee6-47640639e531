import { Drawer, I<PERSON><PERSON><PERSON>on } from "@mui/material";
import { Icon, LogoText } from "./assets";
import styles from "./sidebar.module.css";

const sections = [
  {
    label: "Create request",
    icon: <Icon.CreateRequest />,
    selected: true,
  },
  {
    label: "My requests",
    icon: <Icon.Folder />,
  },
  {
    label: "Dashboard",
    icon: <Icon.Dashboard />,
  },
  {
    label: "Settings",
    icon: <Icon.Settings />,
  },
];

type Props = {
  handleSidebarClose: (state: boolean) => void;
  open: boolean;
};

export const Sidebar = ({ handleSidebarClose, open }: Props) => {
  return (
    <Drawer
      open={open}
      variant="persistent"
      anchor="left"
      sx={{
        width: 190,
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          width: 190,
          boxSizing: "border-box",
        },
      }}
    >
      <aside className={styles.sidebar}>
        <div className={styles.logoContainer}>
          <img src="assets/Logo.svg" />
          <LogoText />
          <IconButton
            edge="end"
            disableRipple
            onClick={() => handleSidebarClose(false)}
            sx={{
              alignSelf: "end",
            }}
          >
            <Icon.Sidebar />
          </IconButton>
        </div>

        <ul className={styles.sections}>
          {sections.map((section) => (
            <li
              key={section.label}
              className={`${styles.section} ${
                section?.selected ? styles.section__selected : undefined
              }`}
            >
              {section.icon}
              {section.label}
            </li>
          ))}
        </ul>
      </aside>
    </Drawer>
  );
};
