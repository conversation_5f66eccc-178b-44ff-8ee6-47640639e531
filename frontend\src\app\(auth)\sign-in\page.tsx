import {
  Box,
  Button,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useState, FormEvent } from "react";
import { Link as RouterLink } from "react-router";
import MailOutlineIcon from "@mui/icons-material/MailOutline";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import { Requester } from "../../../lib/requester";

export function SignInPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const toggleShowPassword = () => {
    setShowPassword(v => !v);
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const result = await Requester.post(
      "/auth/sign-in",
      {
        email,
        password,
      }
    );

    if (result.success) {
      location.assign("/");
    }
    else if (result.error) {
      alert(result.error.message);
    }
    else {
      alert(`${result.status}: ${result.body}`);
    }
  };

  return (
    <Box sx={{ width: "100%", maxWidth: 620 }}>
      {/* Page Title */}
      <Typography
        variant="h3"
        component="h1"
        sx={{
          fontWeight: 500,
          fontSize: { xs: "2.5rem", sm: "3.125rem", md: "3.75rem" },
          lineHeight: 1.2,
          mb: 2.5,
          fontFamily: "'DM Sans', sans-serif",
        }}
      >
        Get Early Access – Free Beta Testing
      </Typography>

      {/* Page Description */}
      <Typography
        variant="body1"
        sx={{
          fontWeight: 300,
          mb: 6.25,
          fontSize: "20px",
          letterSpacing: "0.625px",
          fontFamily: "'DM Sans', sans-serif",
        }}
      >
        Sign up now to be among the first to test Logispot for free during our development phase.
      </Typography>

      {/* Form */}
      <form onSubmit={handleSubmit}>
        <Stack spacing={3}>
          {/* Email Input */}
          <TextField
            fullWidth
            label="Email"
            placeholder="Enter your email address"
            variant="outlined"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            type="email"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <MailOutlineIcon />
                  </InputAdornment>
                ),
              },
              inputLabel: {
                shrink: true,
                sx: {
                  fontWeight: 'bold',
                  fontSize: '14px',
                  transform: 'translate(14px, -6px) scale(1)',
                  '&.MuiInputLabel-shrink': {
                    transform: 'translate(14px, -6px) scale(1)',
                  }
                }
              }
            }}

            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 0,
                height: "70px",
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: "#C6C5CA",
              },
              "& .MuiOutlinedInput-notchedOutline legend": {
                width: 'auto !important',
                padding: '0 5px',
                visibility: 'visible',
              },
              "& .MuiInputLabel-root": {
                color: '#000',
              },
              "& .MuiInputBase-input::placeholder": {
                paddingLeft: "6px",
                fontSize: "18px",
                color: "#C6C5CA",
                opacity: 1,
              }
            }}
          />

          {/* Password Input */}
          <TextField
            fullWidth
            label="Password"
            placeholder="Enter your password"
            type={showPassword ? "text" : "password"}
            variant="outlined"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <img
                      src="/images/key.svg"
                      alt="key"
                      width={24}
                      height={24}
                    />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={toggleShowPassword}
                      edge="end"
                    >
                      {showPassword ? (
                        <VisibilityOutlinedIcon />
                      ) : (
                        <VisibilityOffOutlinedIcon />
                      )}
                    </IconButton>
                  </InputAdornment>
                ),
              },
              inputLabel: {
                shrink: true,
                sx: {
                  fontWeight: 'bold',
                  fontSize: '14px',
                  transform: 'translate(14px, -6px) scale(1)',
                  '&.MuiInputLabel-shrink': {
                    transform: 'translate(14px, -6px) scale(1)',
                  }
                }
              }
            }}

            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 0,
                height: "70px",
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: "#C6C5CA",
              },
              "& .MuiOutlinedInput-notchedOutline legend": {
                width: 'auto !important',
                padding: '0 5px',
                visibility: 'visible',
              },
              "& .MuiInputLabel-root": {
                color: '#000',
              },
              "& .MuiInputBase-input::placeholder": {
                paddingLeft: "6px",
                fontSize: "18px",
                color: "#C6C5CA",
                opacity: 1,
              }
            }}
          />

          {/* Forgot Password Button */}
          <Box sx={{ display: "flex", justifyContent: "flex-start" }}>
            <Button
              component={RouterLink}
              to="/forgot-password"
              sx={{
                color: "#000000",
                fontWeight: 600,
                fontSize: "20px",
                textTransform: "none",
                padding: 0,
                letterSpacing: "0.5px",
                fontFamily: "'DM Sans', sans-serif",
                "&:hover": {
                  backgroundColor: "transparent",
                },
              }}
            >
              Forgot password?
            </Button>
          </Box>

          {/* Login Button */}
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{
              bgcolor: "#70B57D",
              color: "white",
              borderRadius: 0,
              height: "50px",
              textTransform: "none",
              "&:hover": {
                bgcolor: "#5da069",
              },
            }}
          >
            Login
          </Button>

          {/* Sign Up Button */}
          <Button
            component={RouterLink}
            to="/sign-up"
            fullWidth
            variant="outlined"
            sx={{
              color: "#70B57D",
              borderColor: "#70B57D",
              borderRadius: 0,
              height: "50px",
              textTransform: "none",
              "&:hover": {
                borderColor: "#5da069",
                bgcolor: "transparent",
              },
            }}
          >
            New here? Sign up now
          </Button>
        </Stack>
      </form>
    </Box>
  );
}
